'use client'

import React from 'react'
import RichText from '@/components/RichText'
import type { Country, Post } from '@/payload-types'
import { cn } from '@/utilities/ui'
import Link from 'next/link'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuViewport,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu'

type CountryContentProps = {
  country: Country
}

export const CountryContent: React.FC<CountryContentProps> = ({ country }) => {
  return (
    <div className="container py-10">
      <h1 className="text-4xl font-bold text-center mb-4">{country.title}</h1>
      <div className="text-center mb-10 max-w-2xl mx-auto">
        <p>{country.description}</p>
      </div>

      {country.dropdowns && country.dropdowns.length > 0 && (
        <div className="flex justify-center mb-10">
          <CountryNavigation dropdowns={country.dropdowns} />
        </div>
      )}
    </div>
  )
}

type CountryDropdown = NonNullable<Country['dropdowns']>[number]
type DropdownItem = NonNullable<CountryDropdown['items']>[number]

type CountryNavigationProps = {
  dropdowns: NonNullable<Country['dropdowns']>
}

const CountryNavigation: React.FC<CountryNavigationProps> = ({ dropdowns }) => {
  return (
    <NavigationMenu className="max-w-screen-lg mx-auto">
      <NavigationMenuList className="gap-2">
        {dropdowns.map((dropdown, index) => (
          <NavigationMenuItem key={dropdown.id || index}>
            <NavigationMenuTrigger className={navigationMenuTriggerStyle()}>
              {dropdown.title}
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                {dropdown.items.map((item, idx) => (
                  <ListItem
                    key={item.id || idx}
                    title={item.text}
                    description={item.description}
                    href={item.linkedPost ? `/posts/${item.linkedPost.slug || item.linkedPost}` : undefined}
                    linkedPost={item.linkedPost}
                  />
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>
        ))}
      </NavigationMenuList>
      <div className="perspective-[2000px] absolute top-full left-0 flex w-full justify-center">
        <NavigationMenuViewport className="origin-[top_center] relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]" />
      </div>
    </NavigationMenu>
  )
}

interface ListItemProps {
  title: string
  description?: any
  href?: string
  linkedPost?: any
}

const ListItem = React.forwardRef<
  React.ElementRef<"a">,
  React.ComponentPropsWithoutRef<"a"> & ListItemProps
>(({ className, title, description, href, linkedPost, ...props }, ref) => {
  const LinkComponent = href ? Link : 'a';

  return (
    <li>
      <NavigationMenuLink asChild>
        <LinkComponent
          ref={ref}
          href={href || '#'}
          className={cn(
            "block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
            className,
            href ? 'cursor-pointer' : 'cursor-default'
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          {description && (
            <div className="prose prose-sm line-clamp-2 text-sm leading-snug text-muted-foreground mt-1">
              <RichText data={description} />
            </div>
          )}
          {linkedPost && (
            <div className="mt-2 text-xs text-primary">
              Read more →
            </div>
          )}
        </LinkComponent>
      </NavigationMenuLink>
    </li>
  )
})
ListItem.displayName = "ListItem"
