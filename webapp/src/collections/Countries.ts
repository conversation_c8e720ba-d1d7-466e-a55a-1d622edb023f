import type { CollectionConfig } from 'payload'

import { authenticated } from '@/access/authenticated'
import { authenticatedOrPublished } from '@/access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'
import { populatePublishedAt } from '@/hooks/populatePublishedAt'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import { FixedToolbarFeature, InlineToolbarFeature } from '@payloadcms/richtext-lexical'
import { revalidateDelete, revalidatePage } from "@/collections/Pages/hooks/revalidatePage"
import { generatePreviewPath } from '@/utilities/generatePreviewPath';

export const Countries: CollectionConfig = {
  slug: 'countries',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', '_status'],
    livePreview: {
      url: ({ data, req }) => {
        const path = generatePreviewPath({
          slug: typeof data?.slug === 'string' ? data.slug : '',
          collection: 'countries',
          req,
        })

        return path
      },
    },
    preview: (data, { req }) =>
      generatePreviewPath({
        slug: typeof data?.slug === 'string' ? data.slug : '',
        collection: 'countries',
        req,
      }),
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
    },
    {
      name: 'dropdowns',
      type: 'array',
      label: 'Dropdown Boxes',
      minRows: 1,
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          label: 'Dropdown Title',
        },
        {
          name: 'items',
          type: 'array',
          label: 'Dropdown Items',
          minRows: 1,
          fields: [
            {
              name: 'text',
              type: 'text',
              required: true,
              label: 'Item Text',
            },
            {
              name: 'description',
              type: 'richText',
              label: 'Item Description',
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
                },
              }),
            },
            {
              name: 'linkedPost',
              type: 'relationship',
              label: 'Linked Post',
              relationTo: 'posts',
              hasMany: false,
              admin: {
                description: 'Select a post to link to this dropdown item',
              },
            }
          ]
        }
      ],
      admin: {
        components: {
          RowLabel: ({ data, index }) => {
            return data?.title || `Dropdown ${String(index).padStart(2, '0')}`
          }
        }
      }
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
      },
    },
    ...slugField(),
  ],
  hooks: {
    afterChange: [revalidatePage],
    beforeChange: [populatePublishedAt],
    afterDelete: [revalidateDelete],
  },
  versions: {
    drafts: {
      autosave: {
        interval: 100, // We set this interval for optimal live preview
      },
      schedulePublish: true,
    },
    maxPerDoc: 50,
  },
}
